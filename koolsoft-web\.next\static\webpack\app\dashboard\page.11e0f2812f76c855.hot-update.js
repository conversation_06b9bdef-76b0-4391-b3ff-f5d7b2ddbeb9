"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/hooks/useAuth */ \"(app-pages-browser)/./src/lib/hooks/useAuth.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _components_conversions_reports_conversion_statistics_widget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/conversions/reports/conversion-statistics-widget */ \"(app-pages-browser)/./src/components/conversions/reports/conversion-statistics-widget.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n/**\n * Dashboard Page\n *\n * This page serves as the main dashboard for authenticated users.\n * It displays different content based on the user's role with real-time statistics.\n */ function DashboardPage() {\n    _s();\n    _s1();\n    const { user, isAdmin, isManager, isExecutive } = (0,_lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const [dashboardStats, setDashboardStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Fetch real dashboard statistics\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const fetchDashboardStats = {\n                \"DashboardPage.useEffect.fetchDashboardStats\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const response = await fetch('/api/dashboard-stats', {\n                            credentials: 'include'\n                        });\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch dashboard statistics');\n                        }\n                        const data = await response.json();\n                        setDashboardStats(data);\n                    } catch (err) {\n                        const errorMessage = err instanceof Error ? err.message : 'Failed to load dashboard statistics';\n                        setError(errorMessage);\n                        sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error loading dashboard statistics', {\n                            description: errorMessage\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.fetchDashboardStats\"];\n            fetchDashboardStats();\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n                children: [\n                    isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-5 w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500 truncate\",\n                                                        children: \"Admin Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"Access\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/admin\",\n                                        className: \"font-medium text-primary hover:text-primary/80\",\n                                        children: \"Go to Admin Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-5 w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500 truncate\",\n                                                        children: \"Customers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                                className: \"h-6 w-16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 36\n                                                            }, this) : error ? 'Error' : (dashboardStats === null || dashboardStats === void 0 ? void 0 : dashboardStats.totals.customers.toLocaleString()) || '0'\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/customers\",\n                                        className: \"font-medium text-primary hover:text-primary/80\",\n                                        children: \"View all customers\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 bg-success rounded-md p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-5 w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500 truncate\",\n                                                        children: \"Active AMC Contracts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                                className: \"h-6 w-16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 36\n                                                            }, this) : error ? 'Error' : (dashboardStats === null || dashboardStats === void 0 ? void 0 : dashboardStats.active.amcContracts.toLocaleString()) || '0'\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/amc\",\n                                        className: \"font-medium text-primary hover:text-primary/80\",\n                                        children: \"View AMC Management\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 bg-warning rounded-md p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-6 w-6 text-black\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-5 w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500 truncate\",\n                                                        children: \"Pending Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"42\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/service\",\n                                        className: \"font-medium text-primary hover:text-primary/80\",\n                                        children: \"View all services\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 bg-destructive rounded-md p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-5 w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500 truncate\",\n                                                        children: \"Expiring AMC Contracts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"18\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/amc/expiring\",\n                                        className: \"font-medium text-primary hover:text-primary/80\",\n                                        children: \"View expiring contracts\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-5 w-0 flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500 truncate\",\n                                                        children: \"Visit Cards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"Manage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/visit-cards\",\n                                        className: \"font-medium text-primary hover:text-primary/80\",\n                                        children: \"View all visit cards\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 bg-white shadow overflow-hidden sm:rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-5 sm:px-6 bg-primary text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg leading-6 font-medium\",\n                                children: \"Admin Tools\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 max-w-2xl text-sm text-gray-100\",\n                                children: \"Quick access to administrative tools and reports\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg overflow-hidden border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"User Management\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Manage users and permissions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 px-4 py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/users\",\n                                            className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                            children: \"Manage Users →\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg overflow-hidden border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"Activity Logs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View system activity logs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 px-4 py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/activity-logs\",\n                                            className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                            children: \"View Logs →\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg overflow-hidden border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"Email Management\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Manage email templates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 px-4 py-3 flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/admin/email/templates\",\n                                                className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                                children: \"Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/admin/email/preview\",\n                                                className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                                children: \"Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/admin/email/templates/create\",\n                                                className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                                children: \"Create\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg overflow-hidden border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"System Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Configure system settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 px-4 py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/settings\",\n                                            className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                            children: \"Configure Settings →\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg overflow-hidden border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"Reference Data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Manage reference data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 px-4 py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/reference-data\",\n                                            className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                            children: \"Manage Reference Data →\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg overflow-hidden border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"Visit Cards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Manage visit cards\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 px-4 py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/visit-cards\",\n                                            className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                            children: \"Manage Visit Cards →\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg overflow-hidden border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-5 sm:p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 bg-primary rounded-md p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: \"Admin Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Go to full admin dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 px-4 py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin\",\n                                            className: \"text-primary hover:text-primary/80 text-sm font-medium\",\n                                            children: \"Go to Admin Dashboard →\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 19\n            }, this),\n            (isAdmin || isManager || isExecutive) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_conversions_reports_conversion_statistics_widget__WEBPACK_IMPORTED_MODULE_4__.ConversionStatisticsWidget, {\n                    compact: true\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 49\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 55,\n        columnNumber: 10\n    }, this);\n}\n_s(DashboardPage, \"+V0ccZ46b6cWtVlngsHXI7Af47w=\", false, function() {\n    return [\n        _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n_c1 = DashboardPage;\n_s1(DashboardPage, \"DZ4+rCjFt/cwnV4sb3JyhKmp+I8=\", false, function() {\n    return [\n        _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\nvar _c1;\n$RefreshReg$(_c1, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});