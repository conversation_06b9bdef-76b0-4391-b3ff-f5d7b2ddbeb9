{"name": "koolsoft-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "deploy": "node scripts/deploy.js", "generate-secrets": "node scripts/generate-secrets.js", "env:validate": "node scripts/validate-env.js", "db:migrate": "node scripts/db-migrate.js", "db:migrate:reset": "node scripts/db-migrate.js --reset", "db:migrate:prod": "node scripts/db-migrate.js --env production", "db:studio": "npx prisma studio", "db:backup": "node scripts/backup-database.js", "db:remove-legacy": "node scripts/remove-legacy-tables.js", "db:optimize": "node scripts/update-sequences-and-indexes.js", "db:create-reference-tables": "node scripts/create-reference-tables.js", "db:create-history-tables": "node scripts/create-history-detail-tables.js", "db:create-visit-cards": "node scripts/create-visit-cards-table.js", "db:create-email-tables": "node scripts/create-email-system-tables.js", "db:create-activity-logs": "node scripts/create-activity-logs-table.js", "db:complete-migration": "node scripts/complete-migration.js", "db:verify-migration": "node scripts/verify-migration.js", "db:update-indexes": "node scripts/update-indexes.js", "db:update-prisma-schema": "node scripts/update-prisma-schema.js", "db:test-prisma-models": "node scripts/test-prisma-models.js", "db:seed": "ts-node prisma/seed.ts", "db:migrate-users": "node scripts/run-user-migration.js", "repo:migrate": "node scripts/migrate-repositories.js", "repo:remove-legacy": "node scripts/remove-legacy-repositories.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.3", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.7.0", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.75.7", "@types/jspdf": "^1.3.3", "@types/node-cron": "^3.0.11", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "exceljs": "^4.4.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.510.0", "next": "15.3.2", "next-auth": "^4.24.11", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "prisma": "^6.7.0", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@babel/register": "^7.27.1", "@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "ignore-loader": "^0.1.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "null-loader": "^4.0.1", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5"}}