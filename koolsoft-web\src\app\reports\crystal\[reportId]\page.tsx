'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, CalendarIcon, Search, RotateCcw } from 'lucide-react';
import { format } from 'date-fns';
import { AMCSummaryReport } from '@/components/reports/crystal/amc/amc-summary-report';
import { SearchableCustomerSelect } from '@/components/ui/searchable-customer-select';

interface ReportConfig {
  id: string;
  name: string;
  title: string;
  description: string;
  category: string;
  parameters: ReportParameter[];
}

interface ReportParameter {
  key: string;
  label: string;
  type: 'text' | 'date' | 'select' | 'customer' | 'executive';
  required?: boolean;
  options?: { value: string; label: string; }[];
  defaultValue?: any;
}

/**
 * Individual Crystal Report Viewer Page
 * 
 * Provides parameter input and report viewing for specific Crystal Reports
 */
export default function CrystalReportViewerPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const reportId = params.reportId as string;

  const [reportConfig, setReportConfig] = useState<ReportConfig | null>(null);
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [showReport, setShowReport] = useState(false);

  // Report configurations
  const reportConfigs: Record<string, ReportConfig> = {
    'amc-summary': {
      id: 'amc-summary',
      name: 'AMC Summary',
      title: 'AMC Summary Report',
      description: 'Comprehensive summary of Annual Maintenance Contracts with filtering and grouping options',
      category: 'AMC',
      parameters: [
        {
          key: 'startDate',
          label: 'Start Date',
          type: 'date',
          required: false,
        },
        {
          key: 'endDate',
          label: 'End Date',
          type: 'date',
          required: false,
        },
        {
          key: 'customerId',
          label: 'Customer',
          type: 'customer',
          required: false,
        },
        {
          key: 'executiveId',
          label: 'Executive',
          type: 'executive',
          required: false,
        },
        {
          key: 'amcStatus',
          label: 'AMC Status',
          type: 'select',
          required: false,
          options: [
            { value: '', label: 'All Statuses' },
            { value: 'ACTIVE', label: 'Active' },
            { value: 'EXPIRED', label: 'Expired' },
            { value: 'PENDING', label: 'Pending' },
            { value: 'CANCELLED', label: 'Cancelled' },
          ],
        },
        {
          key: 'contractType',
          label: 'Contract Type',
          type: 'select',
          required: false,
          options: [
            { value: '', label: 'All Types' },
            { value: 'STANDARD', label: 'Standard' },
            { value: 'PREMIUM', label: 'Premium' },
            { value: 'BASIC', label: 'Basic' },
          ],
        },
        {
          key: 'groupBy',
          label: 'Group By',
          type: 'select',
          required: false,
          defaultValue: 'none',
          options: [
            { value: 'none', label: 'No Grouping' },
            { value: 'customer', label: 'Customer' },
            { value: 'executive', label: 'Executive' },
            { value: 'status', label: 'Status' },
          ],
        },
      ],
    },
    'warranty-summary': {
      id: 'warranty-summary',
      name: 'Warranty Summary',
      title: 'In-Warranty Summary Report',
      description: 'Summary of all in-warranty products with filtering options',
      category: 'WARRANTY',
      parameters: [
        {
          key: 'startDate',
          label: 'Start Date',
          type: 'date',
          required: false,
        },
        {
          key: 'endDate',
          label: 'End Date',
          type: 'date',
          required: false,
        },
        {
          key: 'customerId',
          label: 'Customer',
          type: 'customer',
          required: false,
        },
        {
          key: 'warrantyStatus',
          label: 'Warranty Status',
          type: 'select',
          required: false,
          options: [
            { value: '', label: 'All Statuses' },
            { value: 'ACTIVE', label: 'Active' },
            { value: 'EXPIRED', label: 'Expired' },
            { value: 'PENDING', label: 'Pending' },
          ],
        },
      ],
    },
    'service-summary': {
      id: 'service-summary',
      name: 'Service Summary',
      title: 'Service Summary Report',
      description: 'Summary of all service reports with filtering options',
      category: 'SERVICE',
      parameters: [
        {
          key: 'startDate',
          label: 'Start Date',
          type: 'date',
          required: false,
        },
        {
          key: 'endDate',
          label: 'End Date',
          type: 'date',
          required: false,
        },
        {
          key: 'customerId',
          label: 'Customer',
          type: 'customer',
          required: false,
        },
        {
          key: 'executiveId',
          label: 'Executive',
          type: 'executive',
          required: false,
        },
        {
          key: 'serviceStatus',
          label: 'Service Status',
          type: 'select',
          required: false,
          options: [
            { value: '', label: 'All Statuses' },
            { value: 'OPEN', label: 'Open' },
            { value: 'COMPLETED', label: 'Completed' },
            { value: 'PENDING', label: 'Pending' },
            { value: 'CANCELLED', label: 'Cancelled' },
          ],
        },
      ],
    },
  };

  // Initialize component
  useEffect(() => {
    const config = reportConfigs[reportId];
    if (config) {
      setReportConfig(config);
      
      // Set default parameters
      const defaultParams: Record<string, any> = {};
      config.parameters.forEach(param => {
        if (param.defaultValue !== undefined) {
          defaultParams[param.key] = param.defaultValue;
        }
      });
      setParameters(defaultParams);
    } else {
      toast({
        title: 'Report Not Found',
        description: `Crystal Report "${reportId}" not found.`,
        variant: 'destructive',
      });
    }
    setIsLoading(false);
  }, [reportId, toast]);

  // Handle parameter change
  const handleParameterChange = (key: string, value: any) => {
    setParameters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Generate report
  const generateReport = () => {
    setShowReport(true);
  };

  // Reset parameters
  const resetParameters = () => {
    const defaultParams: Record<string, any> = {};
    reportConfig?.parameters.forEach(param => {
      if (param.defaultValue !== undefined) {
        defaultParams[param.key] = param.defaultValue;
      }
    });
    setParameters(defaultParams);
    setShowReport(false);
  };

  // Render parameter input
  const renderParameterInput = (param: ReportParameter) => {
    const value = parameters[param.key];

    switch (param.type) {
      case 'date':
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {value ? format(new Date(value), 'PPP') : 'Select date'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={value ? new Date(value) : undefined}
                onSelect={(date) => handleParameterChange(param.key, date?.toISOString().split('T')[0])}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case 'select':
        return (
          <Select value={value || ''} onValueChange={(val) => handleParameterChange(param.key, val)}>
            <SelectTrigger>
              <SelectValue placeholder={`Select ${param.label}`} />
            </SelectTrigger>
            <SelectContent>
              {param.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'customer':
        return (
          <SearchableCustomerSelect
            value={value || ''}
            onValueChange={(val) => handleParameterChange(param.key, val)}
            placeholder={`Select ${param.label}`}
          />
        );

      case 'executive':
        return (
          <Select value={value || ''} onValueChange={(val) => handleParameterChange(param.key, val)}>
            <SelectTrigger>
              <SelectValue placeholder={`Select ${param.label}`} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Executives</SelectItem>
              {/* This would be populated from API */}
              <SelectItem value="exec1">Executive 1</SelectItem>
              <SelectItem value="exec2">Executive 2</SelectItem>
            </SelectContent>
          </Select>
        );

      default:
        return (
          <Input
            type="text"
            value={value || ''}
            onChange={(e) => handleParameterChange(param.key, e.target.value)}
            placeholder={`Enter ${param.label}`}
          />
        );
    }
  };

  // Render report component
  const renderReportComponent = () => {
    switch (reportId) {
      case 'amc-summary':
        return (
          <AMCSummaryReport
            parameters={parameters}
            onParametersChange={setParameters}
          />
        );
      default:
        return (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-gray-500">Report component not yet implemented for {reportId}</p>
            </CardContent>
          </Card>
        );
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!reportConfig) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Report not found</p>
        <Button onClick={() => router.back()} className="mt-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{reportConfig.title}</h1>
            <p className="text-gray-600">{reportConfig.description}</p>
          </div>
        </div>
      </div>

      {/* Parameters */}
      <Card>
        <CardHeader>
          <CardTitle>Report Parameters</CardTitle>
          <CardDescription>Configure the report parameters and generate the report</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {reportConfig.parameters.map((param) => (
              <div key={param.key} className="space-y-2">
                <Label htmlFor={param.key}>
                  {param.label}
                  {param.required && <span className="text-red-500 ml-1">*</span>}
                </Label>
                {renderParameterInput(param)}
              </div>
            ))}
          </div>
          
          <div className="flex items-center space-x-4 mt-6">
            <Button onClick={generateReport}>
              <Search className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button variant="outline" onClick={resetParameters}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Report Display */}
      {showReport && renderReportComponent()}
    </div>
  );
}
