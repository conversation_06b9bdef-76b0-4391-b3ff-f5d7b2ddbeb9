'use client';

/**
 * Formula Editor Component
 * 
 * Provides a rich editor interface for creating and editing formulas
 * with syntax highlighting, validation, and function assistance.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Calculator, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Play, 
  Book,
  Lightbulb,
  Code
} from 'lucide-react';
import { toast } from 'sonner';

interface FormulaEditorProps {
  initialFormula?: string;
  initialName?: string;
  initialDescription?: string;
  onSave?: (data: {
    name: string;
    description?: string;
    formula: string;
    category: string;
    returnType: string;
  }) => void;
  onTest?: (formula: string, testData: any) => void;
  readOnly?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  variables: string[];
  functions: string[];
  complexity: number;
  formattedFormula: string;
}

interface FunctionDoc {
  name: string;
  description: string;
  parameters: string[];
  examples: string[];
}

export function FormulaEditor({
  initialFormula = '',
  initialName = '',
  initialDescription = '',
  onSave,
  onTest,
  readOnly = false,
}: FormulaEditorProps) {
  const [name, setName] = useState(initialName);
  const [description, setDescription] = useState(initialDescription);
  const [formula, setFormula] = useState(initialFormula);
  const [category, setCategory] = useState('CUSTOM');
  const [returnType, setReturnType] = useState('NUMBER');
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [functions, setFunctions] = useState<Record<string, FunctionDoc[]>>({});
  const [selectedFunction, setSelectedFunction] = useState<FunctionDoc | null>(null);
  const [testData, setTestData] = useState('{}');
  const [testResult, setTestResult] = useState<any>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [isTesting, setIsTesting] = useState(false);

  // Load available functions on mount
  useEffect(() => {
    loadFunctions();
  }, []);

  // Validate formula when it changes
  useEffect(() => {
    if (formula.trim()) {
      validateFormula();
    } else {
      setValidation(null);
    }
  }, [formula]);

  const loadFunctions = async () => {
    try {
      const response = await fetch('/api/reports/formulas/evaluate', {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setFunctions(data.data.functions);
      }
    } catch (error) {
      console.error('Error loading functions:', error);
    }
  };

  const validateFormula = useCallback(async () => {
    if (!formula.trim()) return;

    setIsValidating(true);
    try {
      const response = await fetch('/api/reports/formulas/evaluate', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          formula,
          context: testData ? JSON.parse(testData) : undefined,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setValidation(data.data);
      }
    } catch (error) {
      console.error('Error validating formula:', error);
    } finally {
      setIsValidating(false);
    }
  }, [formula, testData]);

  const testFormula = async () => {
    if (!formula.trim()) {
      toast.error('Please enter a formula to test');
      return;
    }

    setIsTesting(true);
    try {
      const context = testData ? JSON.parse(testData) : {};
      
      const response = await fetch('/api/reports/formulas/evaluate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          formula,
          context,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setTestResult(data.data);
        
        if (data.data.success) {
          toast.success('Formula executed successfully');
        } else {
          toast.error(`Formula error: ${data.data.error}`);
        }
      } else {
        toast.error('Failed to test formula');
      }
    } catch (error) {
      console.error('Error testing formula:', error);
      toast.error('Invalid test data JSON');
    } finally {
      setIsTesting(false);
    }
  };

  const insertFunction = (functionName: string) => {
    const func = Object.values(functions)
      .flat()
      .find(f => f.name === functionName);
    
    if (func) {
      const params = func.parameters.map(p => p.replace('?', '')).join(', ');
      const insertion = `${functionName}(${params})`;
      setFormula(prev => prev + insertion);
    }
  };

  const handleSave = () => {
    if (!name.trim()) {
      toast.error('Please enter a formula name');
      return;
    }

    if (!formula.trim()) {
      toast.error('Please enter a formula');
      return;
    }

    if (validation && !validation.isValid) {
      toast.error('Please fix formula errors before saving');
      return;
    }

    onSave?.({
      name: name.trim(),
      description: description.trim() || undefined,
      formula: formula.trim(),
      category,
      returnType,
    });
  };

  const getComplexityColor = (complexity: number) => {
    if (complexity <= 30) return 'bg-green-500';
    if (complexity <= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getComplexityLabel = (complexity: number) => {
    if (complexity <= 30) return 'Simple';
    if (complexity <= 60) return 'Moderate';
    return 'Complex';
  };

  return (
    <div className="space-y-6">
      {/* Formula Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Formula Details
          </CardTitle>
          <CardDescription>
            Define the basic information for your formula
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Formula Name *</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter formula name"
                disabled={readOnly}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={readOnly}
              >
                <option value="MATHEMATICAL">Mathematical</option>
                <option value="STATISTICAL">Statistical</option>
                <option value="BUSINESS">Business</option>
                <option value="CUSTOM">Custom</option>
              </select>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe what this formula calculates"
              rows={2}
              disabled={readOnly}
            />
          </div>
        </CardContent>
      </Card>

      {/* Formula Editor */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Formula Expression
          </CardTitle>
          <CardDescription>
            Write your formula using mathematical expressions and functions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="editor" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="editor">Editor</TabsTrigger>
              <TabsTrigger value="functions">Functions</TabsTrigger>
              <TabsTrigger value="test">Test</TabsTrigger>
            </TabsList>

            <TabsContent value="editor" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="formula">Formula Expression *</Label>
                <Textarea
                  id="formula"
                  value={formula}
                  onChange={(e) => setFormula(e.target.value)}
                  placeholder="Enter your formula (e.g., SUM(amount1, amount2) * 0.18)"
                  rows={4}
                  className="font-mono"
                  disabled={readOnly}
                />
              </div>

              {/* Validation Results */}
              {validation && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {validation.isValid ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <span className="text-sm font-medium">
                        {validation.isValid ? 'Valid Formula' : 'Invalid Formula'}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">Complexity:</span>
                      <Badge variant="outline" className="text-xs">
                        <div className={`w-2 h-2 rounded-full mr-1 ${getComplexityColor(validation.complexity)}`} />
                        {getComplexityLabel(validation.complexity)} ({validation.complexity})
                      </Badge>
                    </div>
                  </div>

                  {validation.errors.length > 0 && (
                    <Alert variant="destructive">
                      <XCircle className="h-4 w-4" />
                      <AlertDescription>
                        <ul className="list-disc list-inside space-y-1">
                          {validation.errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}

                  {validation.warnings.length > 0 && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <ul className="list-disc list-inside space-y-1">
                          {validation.warnings.map((warning, index) => (
                            <li key={index}>{warning}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}

                  {validation.variables.length > 0 && (
                    <div>
                      <Label className="text-sm">Variables Used:</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {validation.variables.map((variable) => (
                          <Badge key={variable} variant="secondary" className="text-xs">
                            {variable}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {validation.functions.length > 0 && (
                    <div>
                      <Label className="text-sm">Functions Used:</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {validation.functions.map((func) => (
                          <Badge key={func} variant="outline" className="text-xs">
                            {func}()
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="functions" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Available Functions</Label>
                  <ScrollArea className="h-64 border rounded-md p-2 mt-2">
                    {Object.entries(functions).map(([category, funcs]) => (
                      <div key={category} className="mb-4">
                        <h4 className="font-medium text-sm text-gray-700 mb-2">{category}</h4>
                        <div className="space-y-1">
                          {funcs.map((func) => (
                            <button
                              key={func.name}
                              onClick={() => setSelectedFunction(func)}
                              className="w-full text-left px-2 py-1 text-xs hover:bg-gray-100 rounded"
                              disabled={readOnly}
                            >
                              <span className="font-mono text-blue-600">{func.name}</span>
                              <span className="text-gray-500 ml-1">
                                ({func.parameters.join(', ')})
                              </span>
                            </button>
                          ))}
                        </div>
                      </div>
                    ))}
                  </ScrollArea>
                </div>

                <div>
                  {selectedFunction && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Function Details</Label>
                        {!readOnly && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => insertFunction(selectedFunction.name)}
                          >
                            Insert
                          </Button>
                        )}
                      </div>
                      
                      <Card>
                        <CardContent className="p-3 space-y-2">
                          <div>
                            <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                              {selectedFunction.name}({selectedFunction.parameters.join(', ')})
                            </code>
                          </div>
                          
                          <p className="text-sm text-gray-600">
                            {selectedFunction.description}
                          </p>
                          
                          {selectedFunction.examples.length > 0 && (
                            <div>
                              <Label className="text-xs">Examples:</Label>
                              <div className="space-y-1 mt-1">
                                {selectedFunction.examples.map((example, index) => (
                                  <code key={index} className="block text-xs bg-gray-50 p-1 rounded">
                                    {example}
                                  </code>
                                ))}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="test" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <Label htmlFor="testData">Test Data (JSON)</Label>
                  <Textarea
                    id="testData"
                    value={testData}
                    onChange={(e) => setTestData(e.target.value)}
                    placeholder='{"amount": 1000, "taxRate": 18}'
                    rows={6}
                    className="font-mono text-sm"
                  />
                  
                  <Button
                    onClick={testFormula}
                    disabled={isTesting || !formula.trim()}
                    className="w-full"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    {isTesting ? 'Testing...' : 'Test Formula'}
                  </Button>
                </div>

                <div className="space-y-3">
                  <Label>Test Result</Label>
                  {testResult && (
                    <Card>
                      <CardContent className="p-3">
                        {testResult.success ? (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-green-600">
                              <CheckCircle className="h-4 w-4" />
                              <span className="text-sm font-medium">Success</span>
                            </div>
                            <div>
                              <Label className="text-xs">Result:</Label>
                              <div className="bg-green-50 p-2 rounded mt-1">
                                <code className="text-sm">
                                  {JSON.stringify(testResult.result, null, 2)}
                                </code>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-red-600">
                              <XCircle className="h-4 w-4" />
                              <span className="text-sm font-medium">Error</span>
                            </div>
                            <div className="bg-red-50 p-2 rounded">
                              <code className="text-sm text-red-700">
                                {testResult.error}
                              </code>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Actions */}
      {!readOnly && onSave && (
        <div className="flex justify-end gap-2">
          <Button
            onClick={handleSave}
            disabled={!name.trim() || !formula.trim() || (validation && !validation.isValid)}
          >
            Save Formula
          </Button>
        </div>
      )}
    </div>
  );
}
