'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { X, Plus, Mail, Send } from 'lucide-react';
import { toast } from 'sonner';

const emailDistributionSchema = z.object({
  configId: z.string().optional(),
  recipients: z.array(z.string().email()).min(1, 'At least one recipient is required'),
  emailSubject: z.string().min(1, 'Email subject is required'),
  emailBody: z.string().min(1, 'Email body is required'),
  includeAttachment: z.boolean().default(true),
  attachmentFormat: z.enum(['PDF', 'EXCEL', 'CSV']).default('PDF'),
});

type EmailDistributionForm = z.infer<typeof emailDistributionSchema>;

interface EmailDistributionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  reportType: string;
  reportParameters: Record<string, any>;
  onSuccess?: () => void;
}

interface EmailConfig {
  id: string;
  name: string;
  emailSubject: string;
  emailBody: string;
  includeAttachment: boolean;
  attachmentFormat: string;
  distributionList?: {
    emails: string[];
  };
  individualRecipients: string[];
}

export function EmailDistributionDialog({
  open,
  onOpenChange,
  reportType,
  reportParameters,
  onSuccess,
}: EmailDistributionDialogProps) {
  const [loading, setLoading] = useState(false);
  const [emailConfigs, setEmailConfigs] = useState<EmailConfig[]>([]);
  const [newRecipient, setNewRecipient] = useState('');

  const form = useForm<EmailDistributionForm>({
    resolver: zodResolver(emailDistributionSchema),
    defaultValues: {
      recipients: [],
      emailSubject: `${reportType} Report`,
      emailBody: `Please find attached the ${reportType} report generated with the specified parameters.`,
      includeAttachment: true,
      attachmentFormat: 'PDF',
    },
  });

  // Load email configurations for this report type
  useEffect(() => {
    if (open && reportType) {
      loadEmailConfigs();
    }
  }, [open, reportType]);

  const loadEmailConfigs = async () => {
    try {
      const response = await fetch(`/api/reports/email/configs?reportType=${reportType}&isActive=true`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setEmailConfigs(data.data || []);
      }
    } catch (error) {
      console.error('Error loading email configurations:', error);
    }
  };

  const handleConfigSelect = (configId: string) => {
    const config = emailConfigs.find(c => c.id === configId);
    if (config) {
      const recipients = [
        ...config.individualRecipients,
        ...(config.distributionList?.emails || []),
      ];

      form.setValue('configId', configId);
      form.setValue('recipients', recipients);
      form.setValue('emailSubject', config.emailSubject);
      form.setValue('emailBody', config.emailBody);
      form.setValue('includeAttachment', config.includeAttachment);
      form.setValue('attachmentFormat', config.attachmentFormat as 'PDF' | 'EXCEL' | 'CSV');
    }
  };

  const addRecipient = () => {
    if (newRecipient && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newRecipient)) {
      const currentRecipients = form.getValues('recipients');
      if (!currentRecipients.includes(newRecipient)) {
        form.setValue('recipients', [...currentRecipients, newRecipient]);
        setNewRecipient('');
      } else {
        toast.error('Recipient already added');
      }
    } else {
      toast.error('Please enter a valid email address');
    }
  };

  const removeRecipient = (email: string) => {
    const currentRecipients = form.getValues('recipients');
    form.setValue('recipients', currentRecipients.filter(r => r !== email));
  };

  const onSubmit = async (data: EmailDistributionForm) => {
    setLoading(true);
    try {
      const response = await fetch('/api/reports/email/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...data,
          reportType,
          reportParameters,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message);
        onOpenChange(false);
        onSuccess?.();
        form.reset();
      } else {
        toast.error(result.error || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error('Failed to send email');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Report Distribution
          </DialogTitle>
          <DialogDescription>
            Send the {reportType} report via email to specified recipients.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Email Configuration Selection */}
            {emailConfigs.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Use Saved Configuration (Optional)</label>
                <Select onValueChange={handleConfigSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a saved email configuration" />
                  </SelectTrigger>
                  <SelectContent>
                    {emailConfigs.map((config) => (
                      <SelectItem key={config.id} value={config.id}>
                        {config.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Recipients */}
            <div className="space-y-3">
              <FormLabel>Recipients</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter email address"
                  value={newRecipient}
                  onChange={(e) => setNewRecipient(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addRecipient())}
                />
                <Button type="button" onClick={addRecipient} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {form.watch('recipients').map((email) => (
                  <Badge key={email} variant="secondary" className="flex items-center gap-1">
                    {email}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeRecipient(email)}
                    />
                  </Badge>
                ))}
              </div>
              {form.formState.errors.recipients && (
                <p className="text-sm text-red-500">{form.formState.errors.recipients.message}</p>
              )}
            </div>

            {/* Email Subject */}
            <FormField
              control={form.control}
              name="emailSubject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Subject</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email Body */}
            <FormField
              control={form.control}
              name="emailBody"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Body</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={4} />
                  </FormControl>
                  <FormDescription>
                    You can use placeholders like {'{reportType}'}, {'{reportDate}'}, etc.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Attachment Options */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="includeAttachment"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Include Report Attachment</FormLabel>
                      <FormDescription>
                        Attach the generated report file to the email
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {form.watch('includeAttachment') && (
                <FormField
                  control={form.control}
                  name="attachmentFormat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Attachment Format</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="PDF">PDF</SelectItem>
                          <SelectItem value="EXCEL">Excel</SelectItem>
                          <SelectItem value="CSV">CSV</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  'Sending...'
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Email
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
