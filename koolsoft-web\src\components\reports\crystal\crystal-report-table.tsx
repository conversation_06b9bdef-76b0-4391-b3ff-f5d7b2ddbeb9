'use client';

import React, { useMemo } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { format } from 'date-fns';

export interface CrystalTableColumn {
  key: string;
  label: string;
  type?: 'text' | 'number' | 'currency' | 'date' | 'datetime' | 'boolean' | 'status';
  width?: string;
  align?: 'left' | 'center' | 'right';
  format?: string;
  sortable?: boolean;
  groupable?: boolean;
  summable?: boolean;
}

export interface CrystalTableGroup {
  field: string;
  value: any;
  label: string;
  rows: any[];
  summary?: Record<string, any>;
}

export interface CrystalReportTableProps {
  data: any[];
  columns: CrystalTableColumn[];
  groupBy?: string;
  showGroupSummary?: boolean;
  showGrandTotal?: boolean;
  className?: string;
  maxHeight?: string;
}

/**
 * Crystal Report Table Component
 * 
 * Replicates Crystal Reports table functionality including:
 * - Grouping and sub-totals
 * - Data type formatting
 * - Professional styling
 * - Summary calculations
 */
export function CrystalReportTable({
  data,
  columns,
  groupBy,
  showGroupSummary = false,
  showGrandTotal = false,
  className = '',
  maxHeight = '600px',
}: CrystalReportTableProps) {
  
  /**
   * Format cell value based on column type
   */
  const formatCellValue = (value: any, column: CrystalTableColumn): string => {
    if (value === null || value === undefined) return '';

    switch (column.type) {
      case 'currency':
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
        }).format(Number(value) || 0);
      
      case 'number':
        return new Intl.NumberFormat('en-IN').format(Number(value) || 0);
      
      case 'date':
        return value ? format(new Date(value), 'dd-MMM-yyyy') : '';
      
      case 'datetime':
        return value ? format(new Date(value), 'dd-MMM-yyyy HH:mm') : '';
      
      case 'boolean':
        return value ? 'Yes' : 'No';
      
      case 'status':
        return String(value).toUpperCase();
      
      default:
        return String(value);
    }
  };

  /**
   * Calculate summary for a group of rows
   */
  const calculateSummary = (rows: any[], columns: CrystalTableColumn[]): Record<string, any> => {
    const summary: Record<string, any> = {};
    
    columns.forEach(column => {
      if (column.summable && (column.type === 'number' || column.type === 'currency')) {
        summary[column.key] = rows.reduce((sum, row) => {
          const value = Number(row[column.key]) || 0;
          return sum + value;
        }, 0);
      }
    });
    
    return summary;
  };

  /**
   * Group data by specified field
   */
  const groupedData = useMemo(() => {
    if (!groupBy) return null;

    const groups: Record<string, any[]> = {};
    
    data.forEach(row => {
      const groupValue = row[groupBy];
      const groupKey = String(groupValue || 'Unknown');
      
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(row);
    });

    return Object.entries(groups).map(([key, rows]) => ({
      field: groupBy,
      value: key,
      label: key,
      rows,
      summary: showGroupSummary ? calculateSummary(rows, columns) : undefined,
    }));
  }, [data, groupBy, showGroupSummary, columns]);

  /**
   * Calculate grand total
   */
  const grandTotal = useMemo(() => {
    if (!showGrandTotal) return null;
    return calculateSummary(data, columns);
  }, [data, showGrandTotal, columns]);

  /**
   * Render table header
   */
  const renderTableHeader = () => (
    <TableHeader>
      <TableRow className="bg-gray-100">
        {columns.map((column) => (
          <TableHead
            key={column.key}
            className={`font-bold text-gray-900 ${
              column.align === 'center' ? 'text-center' : 
              column.align === 'right' ? 'text-right' : 'text-left'
            }`}
            style={{ width: column.width }}
          >
            {column.label}
          </TableHead>
        ))}
      </TableRow>
    </TableHeader>
  );

  /**
   * Render table row
   */
  const renderTableRow = (row: any, index: number, isGroupSummary = false) => (
    <TableRow 
      key={`row-${index}`}
      className={isGroupSummary ? 'bg-gray-50 font-semibold border-t-2' : 'hover:bg-gray-50'}
    >
      {columns.map((column) => (
        <TableCell
          key={column.key}
          className={`${
            column.align === 'center' ? 'text-center' : 
            column.align === 'right' ? 'text-right' : 'text-left'
          } ${isGroupSummary ? 'font-semibold' : ''}`}
        >
          {isGroupSummary && column.summable ? 
            formatCellValue(row[column.key], column) : 
            formatCellValue(row[column.key], column)
          }
        </TableCell>
      ))}
    </TableRow>
  );

  /**
   * Render group header
   */
  const renderGroupHeader = (group: CrystalTableGroup) => (
    <TableRow key={`group-${group.value}`} className="bg-primary text-white">
      <TableCell colSpan={columns.length} className="font-bold py-3">
        {group.field}: {group.label} ({group.rows.length} records)
      </TableCell>
    </TableRow>
  );

  /**
   * Render group summary
   */
  const renderGroupSummary = (group: CrystalTableGroup) => {
    if (!group.summary) return null;
    
    return (
      <TableRow className="bg-gray-100 font-semibold border-t-2">
        <TableCell className="font-bold">Subtotal:</TableCell>
        {columns.slice(1).map((column) => (
          <TableCell
            key={`summary-${column.key}`}
            className={`font-semibold ${
              column.align === 'center' ? 'text-center' : 
              column.align === 'right' ? 'text-right' : 'text-left'
            }`}
          >
            {column.summable ? formatCellValue(group.summary[column.key], column) : ''}
          </TableCell>
        ))}
      </TableRow>
    );
  };

  /**
   * Render grand total
   */
  const renderGrandTotal = () => {
    if (!grandTotal) return null;
    
    return (
      <TableRow className="bg-primary text-white font-bold border-t-4">
        <TableCell className="font-bold">Grand Total:</TableCell>
        {columns.slice(1).map((column) => (
          <TableCell
            key={`total-${column.key}`}
            className={`font-bold ${
              column.align === 'center' ? 'text-center' : 
              column.align === 'right' ? 'text-right' : 'text-left'
            }`}
          >
            {column.summable ? formatCellValue(grandTotal[column.key], column) : ''}
          </TableCell>
        ))}
      </TableRow>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No data available for the selected criteria.</p>
      </div>
    );
  }

  return (
    <div className={`border rounded-lg overflow-hidden ${className}`}>
      <div 
        className="overflow-auto"
        style={{ maxHeight }}
      >
        <Table>
          {renderTableHeader()}
          <TableBody>
            {groupedData ? (
              // Render grouped data
              groupedData.map((group) => (
                <React.Fragment key={`group-fragment-${group.value}`}>
                  {renderGroupHeader(group)}
                  {group.rows.map((row, index) => renderTableRow(row, index))}
                  {renderGroupSummary(group)}
                </React.Fragment>
              ))
            ) : (
              // Render ungrouped data
              data.map((row, index) => renderTableRow(row, index))
            )}
            {renderGrandTotal()}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

/**
 * Crystal Report Summary Card Component
 */
export function CrystalReportSummary({
  title,
  data,
  className = '',
}: {
  title: string;
  data: Record<string, any>;
  className?: string;
}) {
  return (
    <div className={`bg-gray-50 p-4 rounded-lg border ${className}`}>
      <h3 className="font-bold text-gray-900 mb-3">{title}</h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {Object.entries(data).map(([key, value]) => (
          <div key={key} className="text-center">
            <div className="text-2xl font-bold text-primary">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </div>
            <div className="text-sm text-gray-600 capitalize">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
