import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { EmailDistributionListRepository } from '@/lib/repositories/email-distribution.repository';
import {
  createEmailDistributionListSchema,
  listEmailDistributionListsSchema,
} from '@/lib/validations/email-distribution.schema';
import { z } from 'zod';

/**
 * GET /api/reports/email/distribution-lists
 * List email distribution lists
 */
export const GET = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], async (request: NextRequest, { user }) => {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      search: searchParams.get('search') || undefined,
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
    };

    const validatedParams = listEmailDistributionListsSchema.parse(queryParams);
    const repository = new EmailDistributionListRepository();

    const result = await repository.findByUser(user.id, validatedParams);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });

  } catch (error) {
    console.error('Error fetching distribution lists:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid parameters', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch distribution lists' 
      },
      { status: 500 }
    );
  }
});

/**
 * POST /api/reports/email/distribution-lists
 * Create a new email distribution list
 */
export const POST = withRoleProtection(['ADMIN', 'MANAGER'], async (request: NextRequest, { user }) => {
  try {
    const body = await request.json();
    const validatedData = createEmailDistributionListSchema.parse(body);

    const repository = new EmailDistributionListRepository();

    // Check if name already exists for this user
    const existing = await repository.model.findFirst({
      where: {
        name: validatedData.name,
        createdBy: user.id,
      },
    });

    if (existing) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Distribution list with this name already exists' 
        },
        { status: 409 }
      );
    }

    const distributionList = await repository.create({
      ...validatedData,
      createdBy: user.id,
    });

    return NextResponse.json({
      success: true,
      data: distributionList,
      message: 'Distribution list created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating distribution list:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create distribution list' 
      },
      { status: 500 }
    );
  }
});
