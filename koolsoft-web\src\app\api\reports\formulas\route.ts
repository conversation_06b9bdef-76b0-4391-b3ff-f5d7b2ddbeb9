/**
 * Formula Management API Routes
 * 
 * Handles CRUD operations for report formulas with proper validation,
 * authentication, and error handling.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/lib/auth';
import { withRoleProtection } from '@/lib/auth/middleware';
import { FormulaRepository, createFormulaSchema } from '@/lib/repositories/formula.repository';

const formulaRepository = new FormulaRepository();

// Query parameters schema
const getFormulasSchema = z.object({
  category: z.string().optional(),
  isTemplate: z.string().transform(val => val === 'true').optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  createdBy: z.string().optional(),
  search: z.string().optional(),
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => Math.min(parseInt(val) || 50, 100)).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

/**
 * GET /api/reports/formulas
 * Get formulas with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const filters = getFormulasSchema.parse(queryParams);

    const result = await formulaRepository.getFormulas(filters);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching formulas:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch formulas' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reports/formulas
 * Create a new formula
 */
export async function POST(request: NextRequest) {
  return withRoleProtection(['ADMIN', 'MANAGER'], async (session) => {
    try {
      const body = await request.json();
      const validatedData = createFormulaSchema.parse(body);

      const formula = await formulaRepository.createFormula(
        validatedData,
        session.user.id
      );

      return NextResponse.json({
        success: true,
        data: formula,
        message: 'Formula created successfully',
      }, { status: 201 });
    } catch (error) {
      console.error('Error creating formula:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid formula data', details: error.errors },
          { status: 400 }
        );
      }

      if (error.code === 'P2002') {
        return NextResponse.json(
          { error: 'Formula with this name already exists' },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to create formula' },
        { status: 500 }
      );
    }
  })(request);
}
