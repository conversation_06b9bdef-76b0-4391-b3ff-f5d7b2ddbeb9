'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Save, X, Plus, Trash2 } from 'lucide-react';
import { createScheduledReportSchema, CRON_PRESETS } from '@/lib/validations/scheduled-report.schema';

type FormData = z.infer<typeof createScheduledReportSchema>;

export default function NewScheduledReportPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [emailRecipients, setEmailRecipients] = useState<string[]>(['']);
  const [customCron, setCustomCron] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(createScheduledReportSchema),
    defaultValues: {
      isActive: true,
      exportFormat: 'PDF',
      cronExpression: CRON_PRESETS.DAILY,
    },
  });

  const watchedValues = watch();

  // Handle email recipients
  const addEmailRecipient = () => {
    setEmailRecipients([...emailRecipients, '']);
  };

  const removeEmailRecipient = (index: number) => {
    const newRecipients = emailRecipients.filter((_, i) => i !== index);
    setEmailRecipients(newRecipients);
    setValue('emailRecipients', newRecipients.filter(email => email.trim() !== ''));
  };

  const updateEmailRecipient = (index: number, value: string) => {
    const newRecipients = [...emailRecipients];
    newRecipients[index] = value;
    setEmailRecipients(newRecipients);
    setValue('emailRecipients', newRecipients.filter(email => email.trim() !== ''));
  };

  // Handle cron expression
  const handleCronPreset = (preset: string) => {
    if (preset === 'custom') {
      setCustomCron(true);
      setValue('cronExpression', '');
    } else {
      setCustomCron(false);
      setValue('cronExpression', preset);
    }
  };

  // Submit form
  const onSubmit = async (data: FormData) => {
    try {
      setLoading(true);

      // Filter out empty email recipients
      const validRecipients = emailRecipients.filter(email => email.trim() !== '');
      if (validRecipients.length === 0) {
        toast.error('At least one email recipient is required');
        return;
      }

      const submitData = {
        ...data,
        emailRecipients: validRecipients,
      };

      const response = await fetch('/api/reports/schedules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        toast.success('Scheduled report created successfully');
        router.push('/reports/schedules');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create scheduled report');
      }
    } catch (error) {
      console.error('Error creating scheduled report:', error);
      toast.error('Failed to create scheduled report');
    } finally {
      setLoading(false);
    }
  };

  const breadcrumbs = [
    { label: 'Reports', href: '/reports' },
    { label: 'Scheduled Reports', href: '/reports/schedules' },
    { label: 'New Schedule', href: '/reports/schedules/new' },
  ];

  return (
    <DashboardLayout
      title="New Scheduled Report"
      breadcrumbs={breadcrumbs}
      requireAuth
      allowedRoles={['ADMIN', 'MANAGER']}
    >
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-primary">Create Scheduled Report</CardTitle>
          <CardDescription>
            Set up automated report generation and delivery
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Report Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="e.g., Weekly AMC Summary"
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="reportType">Report Type *</Label>
                <Select
                  value={watchedValues.reportType || ''}
                  onValueChange={(value) => setValue('reportType', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select report type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AMC">AMC Reports</SelectItem>
                    <SelectItem value="WARRANTY">Warranty Reports</SelectItem>
                    <SelectItem value="SERVICE">Service Reports</SelectItem>
                    <SelectItem value="SALES">Sales Reports</SelectItem>
                    <SelectItem value="CUSTOMER">Customer Reports</SelectItem>
                  </SelectContent>
                </Select>
                {errors.reportType && (
                  <p className="text-sm text-red-600">{errors.reportType.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Optional description of the scheduled report"
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Schedule Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Schedule Configuration</h3>
              
              <div className="space-y-2">
                <Label>Schedule Frequency</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {Object.entries(CRON_PRESETS).map(([key, value]) => (
                    <Button
                      key={key}
                      type="button"
                      variant={watchedValues.cronExpression === value ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleCronPreset(value)}
                    >
                      {key.charAt(0) + key.slice(1).toLowerCase()}
                    </Button>
                  ))}
                  <Button
                    type="button"
                    variant={customCron ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleCronPreset('custom')}
                  >
                    Custom
                  </Button>
                </div>
              </div>

              {customCron && (
                <div className="space-y-2">
                  <Label htmlFor="cronExpression">Custom Cron Expression *</Label>
                  <Input
                    id="cronExpression"
                    {...register('cronExpression')}
                    placeholder="e.g., 0 9 * * 1 (Monday at 9 AM)"
                  />
                  <p className="text-sm text-gray-500">
                    Format: minute hour day month day-of-week
                  </p>
                  {errors.cronExpression && (
                    <p className="text-sm text-red-600">{errors.cronExpression.message}</p>
                  )}
                </div>
              )}
            </div>

            {/* Email Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Email Configuration</h3>
              
              <div className="space-y-2">
                <Label>Email Recipients *</Label>
                {emailRecipients.map((email, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => updateEmailRecipient(index, e.target.value)}
                      placeholder="<EMAIL>"
                    />
                    {emailRecipients.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeEmailRecipient(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addEmailRecipient}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Recipient
                </Button>
                {errors.emailRecipients && (
                  <p className="text-sm text-red-600">{errors.emailRecipients.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="emailSubject">Email Subject</Label>
                  <Input
                    id="emailSubject"
                    {...register('emailSubject')}
                    placeholder="Custom email subject (optional)"
                  />
                  {errors.emailSubject && (
                    <p className="text-sm text-red-600">{errors.emailSubject.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="exportFormat">Export Format</Label>
                  <Select
                    value={watchedValues.exportFormat || 'PDF'}
                    onValueChange={(value) => setValue('exportFormat', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PDF">PDF</SelectItem>
                      <SelectItem value="EXCEL">Excel</SelectItem>
                      <SelectItem value="CSV">CSV</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.exportFormat && (
                    <p className="text-sm text-red-600">{errors.exportFormat.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="emailBody">Email Body</Label>
                <Textarea
                  id="emailBody"
                  {...register('emailBody')}
                  placeholder="Custom email message (optional)"
                  rows={4}
                />
                {errors.emailBody && (
                  <p className="text-sm text-red-600">{errors.emailBody.message}</p>
                )}
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={watchedValues.isActive}
                onCheckedChange={(checked) => setValue('isActive', checked)}
              />
              <Label htmlFor="isActive">Active</Label>
              <p className="text-sm text-gray-500">
                {watchedValues.isActive ? 'Schedule is active' : 'Schedule is inactive'}
              </p>
            </div>

            {/* Actions */}
            <div className="flex gap-4 pt-6">
              <Button
                type="submit"
                disabled={loading}
                className="bg-primary hover:bg-primary/90"
              >
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Creating...' : 'Create Schedule'}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/reports/schedules')}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
}
