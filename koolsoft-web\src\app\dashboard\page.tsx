'use client';

import { useAuth } from '@/lib/hooks/useAuth';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  LayoutDashboard,
  Users,
  ShieldCheck,
  Clock,
  AlertTriangle,
  FileText,
  Activity,
  Mail,
  Database,
  Cog
} from 'lucide-react';
import { ConversionStatisticsWidget } from '@/components/conversions/reports/conversion-statistics-widget';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';

interface DashboardStats {
  totals: {
    customers: number;
    amcContracts: number;
    warranties: number;
    machines: number;
  };
  active: {
    amcContracts: number;
    warranties: number;
  };
  expired: {
    amcContracts: number;
    warranties: number;
  };
  expiringSoon: {
    amcContracts: number;
    warranties: number;
  };
  newItems: {
    customers: number;
    amcContracts: number;
    warranties: number;
    machines: number;
  };
}

/**
 * Dashboard Page
 *
 * This page serves as the main dashboard for authenticated users.
 * It displays different content based on the user's role with real-time statistics.
 */
export default function DashboardPage() {
  const { user, isAd<PERSON>, isManager, isExecutive } = useAuth();
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch real dashboard statistics
  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/dashboard-stats', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch dashboard statistics');
        }

        const data = await response.json();
        setDashboardStats(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load dashboard statistics';
        setError(errorMessage);
        toast.error('Error loading dashboard statistics', {
          description: errorMessage,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardStats();
  }, []);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {/* Admin Dashboard Card - Only visible to admins */}
        {isAdmin && (
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-primary rounded-md p-3">
                  <LayoutDashboard className="h-6 w-6 text-white" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Admin Dashboard
                    </dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">
                        Access
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-4 sm:px-6">
              <div className="text-sm">
                <Link href="/admin" className="font-medium text-primary hover:text-primary/80">
                  Go to Admin Dashboard
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Summary Cards */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-primary rounded-md p-3">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Customers
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : error ? (
                        'Error'
                      ) : (
                        dashboardStats?.totals.customers.toLocaleString() || '0'
                      )}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link href="/customers" className="font-medium text-primary hover:text-primary/80">
                View all customers
              </Link>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-success rounded-md p-3">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active AMC Contracts
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : error ? (
                        'Error'
                      ) : (
                        dashboardStats?.active.amcContracts.toLocaleString() || '0'
                      )}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link href="/amc" className="font-medium text-primary hover:text-primary/80">
                View AMC Management
              </Link>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-warning rounded-md p-3">
                <Clock className="h-6 w-6 text-black" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Warranties
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : error ? (
                        'Error'
                      ) : (
                        dashboardStats?.active.warranties.toLocaleString() || '0'
                      )}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link href="/warranties" className="font-medium text-primary hover:text-primary/80">
                View all warranties
              </Link>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-destructive rounded-md p-3">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Expiring AMC Contracts
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : error ? (
                        'Error'
                      ) : (
                        dashboardStats?.expiringSoon.amcContracts.toLocaleString() || '0'
                      )}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link href="/amc/expiring" className="font-medium text-primary hover:text-primary/80">
                View expiring contracts
              </Link>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-primary rounded-md p-3">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Machines
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : error ? (
                        'Error'
                      ) : (
                        dashboardStats?.totals.machines.toLocaleString() || '0'
                      )}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link href="/visit-cards" className="font-medium text-primary hover:text-primary/80">
                View all visit cards
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Role-specific content */}
      {isAdmin && (
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6 bg-primary text-white">
            <h3 className="text-lg leading-6 font-medium">
              Admin Tools
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-100">
              Quick access to administrative tools and reports
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
            {/* User Management */}
            <div className="bg-white shadow rounded-lg overflow-hidden border border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">User Management</h3>
                    <p className="text-sm text-gray-500">Manage users and permissions</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3">
                <Link href="/admin/users" className="text-primary hover:text-primary/80 text-sm font-medium">
                  Manage Users →
                </Link>
              </div>
            </div>

            {/* Activity Logs */}
            <div className="bg-white shadow rounded-lg overflow-hidden border border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">Activity Logs</h3>
                    <p className="text-sm text-gray-500">View system activity logs</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3">
                <Link href="/admin/activity-logs" className="text-primary hover:text-primary/80 text-sm font-medium">
                  View Logs →
                </Link>
              </div>
            </div>

            {/* Email Management */}
            <div className="bg-white shadow rounded-lg overflow-hidden border border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <Mail className="h-5 w-5 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">Email Management</h3>
                    <p className="text-sm text-gray-500">Manage email templates</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 flex justify-between">
                <Link href="/admin/email/templates" className="text-primary hover:text-primary/80 text-sm font-medium">
                  Templates
                </Link>
                <Link href="/admin/email/preview" className="text-primary hover:text-primary/80 text-sm font-medium">
                  Preview
                </Link>
                <Link href="/admin/email/templates/create" className="text-primary hover:text-primary/80 text-sm font-medium">
                  Create
                </Link>
              </div>
            </div>

            {/* System Settings */}
            <div className="bg-white shadow rounded-lg overflow-hidden border border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <Cog className="h-5 w-5 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">System Settings</h3>
                    <p className="text-sm text-gray-500">Configure system settings</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3">
                <Link href="/admin/settings" className="text-primary hover:text-primary/80 text-sm font-medium">
                  Configure Settings →
                </Link>
              </div>
            </div>

            {/* Reference Data */}
            <div className="bg-white shadow rounded-lg overflow-hidden border border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <Database className="h-5 w-5 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">Reference Data</h3>
                    <p className="text-sm text-gray-500">Manage reference data</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3">
                <Link href="/reference-data" className="text-primary hover:text-primary/80 text-sm font-medium">
                  Manage Reference Data →
                </Link>
              </div>
            </div>

            {/* Visit Cards */}
            <div className="bg-white shadow rounded-lg overflow-hidden border border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <FileText className="h-5 w-5 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">Visit Cards</h3>
                    <p className="text-sm text-gray-500">Manage visit cards</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3">
                <Link href="/visit-cards" className="text-primary hover:text-primary/80 text-sm font-medium">
                  Manage Visit Cards →
                </Link>
              </div>
            </div>

            {/* Admin Dashboard */}
            <div className="bg-white shadow rounded-lg overflow-hidden border border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <LayoutDashboard className="h-5 w-5 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">Admin Dashboard</h3>
                    <p className="text-sm text-gray-500">Go to full admin dashboard</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3">
                <Link href="/admin" className="text-primary hover:text-primary/80 text-sm font-medium">
                  Go to Admin Dashboard →
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conversion Statistics Widget - Visible to ADMIN, MANAGER, EXECUTIVE */}
      {(isAdmin || isManager || isExecutive) && (
        <div className="mt-8">
          <ConversionStatisticsWidget compact={true} />
        </div>
      )}
    </div>
  );
}
