/**
 * Formula Engine Test Suite
 * 
 * Basic tests to verify the formula engine functionality
 */

import { FormulaEngine } from './index';

export function runFormulaEngineTests() {
  console.log('Running Formula Engine Tests...');
  
  const engine = new FormulaEngine();
  const tests = [
    // Basic arithmetic
    { formula: '2 + 3', expected: 5, description: 'Basic addition' },
    { formula: '10 - 4', expected: 6, description: 'Basic subtraction' },
    { formula: '3 * 4', expected: 12, description: 'Basic multiplication' },
    { formula: '15 / 3', expected: 5, description: 'Basic division' },
    { formula: '2 ^ 3', expected: 8, description: 'Exponentiation' },
    
    // Functions
    { formula: 'ABS(-5)', expected: 5, description: 'Absolute value' },
    { formula: 'ROUND(3.14159, 2)', expected: 3.14, description: 'Rounding' },
    { formula: 'MAX(1, 5, 3)', expected: 5, description: 'Maximum value' },
    { formula: 'MIN(1, 5, 3)', expected: 1, description: 'Minimum value' },
    { formula: 'SUM(1, 2, 3, 4)', expected: 10, description: 'Sum function' },
    { formula: 'AVERAGE(2, 4, 6)', expected: 4, description: 'Average function' },
    
    // Business functions
    { formula: 'PERCENTAGE(25, 100)', expected: 25, description: 'Percentage calculation' },
    { formula: 'GROWTH(120, 100)', expected: 20, description: 'Growth calculation' },
    { formula: 'DISCOUNT(100, 10)', expected: 90, description: 'Discount calculation' },
    { formula: 'TAX(100, 18)', expected: 18, description: 'Tax calculation' },
    
    // Complex expressions
    { formula: '(2 + 3) * 4', expected: 20, description: 'Parentheses precedence' },
    { formula: 'SUM(1, 2) + MAX(3, 4)', expected: 7, description: 'Function combination' },
    { formula: 'ROUND(PERCENTAGE(33, 100), 1)', expected: 33.0, description: 'Nested functions' },
  ];
  
  let passed = 0;
  let failed = 0;
  
  tests.forEach((test, index) => {
    try {
      const result = engine.evaluate(test.formula);
      
      if (result.success && result.value === test.expected) {
        console.log(`✓ Test ${index + 1}: ${test.description} - PASSED`);
        passed++;
      } else {
        console.log(`✗ Test ${index + 1}: ${test.description} - FAILED`);
        console.log(`  Formula: ${test.formula}`);
        console.log(`  Expected: ${test.expected}`);
        console.log(`  Got: ${result.value}`);
        console.log(`  Error: ${result.error}`);
        failed++;
      }
    } catch (error) {
      console.log(`✗ Test ${index + 1}: ${test.description} - ERROR`);
      console.log(`  Formula: ${test.formula}`);
      console.log(`  Error: ${error.message}`);
      failed++;
    }
  });
  
  console.log(`\nTest Results: ${passed} passed, ${failed} failed`);
  return { passed, failed, total: tests.length };
}

// Context-based tests
export function runContextTests() {
  console.log('\nRunning Context Tests...');
  
  const engine = new FormulaEngine();
  const context = {
    amount: 1000,
    taxRate: 18,
    discountRate: 10,
    customer: {
      name: 'Test Customer',
      type: 'Premium'
    }
  };
  
  const tests = [
    { 
      formula: 'amount * taxRate / 100', 
      expected: 180, 
      description: 'Tax calculation with context' 
    },
    { 
      formula: 'amount - (amount * discountRate / 100)', 
      expected: 900, 
      description: 'Discount calculation with context' 
    },
    { 
      formula: 'customer.name', 
      expected: 'Test Customer', 
      description: 'Nested property access' 
    },
    { 
      formula: 'IF(customer.type == "Premium", amount * 0.95, amount)', 
      expected: 950, 
      description: 'Conditional calculation' 
    },
  ];
  
  let passed = 0;
  let failed = 0;
  
  tests.forEach((test, index) => {
    try {
      const result = engine.evaluate(test.formula, context);
      
      if (result.success && result.value === test.expected) {
        console.log(`✓ Context Test ${index + 1}: ${test.description} - PASSED`);
        passed++;
      } else {
        console.log(`✗ Context Test ${index + 1}: ${test.description} - FAILED`);
        console.log(`  Formula: ${test.formula}`);
        console.log(`  Expected: ${test.expected}`);
        console.log(`  Got: ${result.value}`);
        console.log(`  Error: ${result.error}`);
        failed++;
      }
    } catch (error) {
      console.log(`✗ Context Test ${index + 1}: ${test.description} - ERROR`);
      console.log(`  Formula: ${test.formula}`);
      console.log(`  Error: ${error.message}`);
      failed++;
    }
  });
  
  console.log(`\nContext Test Results: ${passed} passed, ${failed} failed`);
  return { passed, failed, total: tests.length };
}

// Validation tests
export function runValidationTests() {
  console.log('\nRunning Validation Tests...');
  
  const engine = new FormulaEngine();
  
  const tests = [
    { 
      formula: 'SUM(1, 2, 3)', 
      shouldBeValid: true, 
      description: 'Valid formula' 
    },
    { 
      formula: 'INVALID_FUNCTION(1)', 
      shouldBeValid: false, 
      description: 'Invalid function' 
    },
    { 
      formula: '1 / 0', 
      shouldBeValid: false, 
      description: 'Division by zero' 
    },
    { 
      formula: 'SUM(', 
      shouldBeValid: false, 
      description: 'Incomplete formula' 
    },
    { 
      formula: 'MAX(1, 2, 3) + MIN(4, 5)', 
      shouldBeValid: true, 
      description: 'Complex valid formula' 
    },
  ];
  
  let passed = 0;
  let failed = 0;
  
  tests.forEach((test, index) => {
    try {
      const validation = engine.validate(test.formula);
      
      if (validation.isValid === test.shouldBeValid) {
        console.log(`✓ Validation Test ${index + 1}: ${test.description} - PASSED`);
        passed++;
      } else {
        console.log(`✗ Validation Test ${index + 1}: ${test.description} - FAILED`);
        console.log(`  Formula: ${test.formula}`);
        console.log(`  Expected valid: ${test.shouldBeValid}`);
        console.log(`  Got valid: ${validation.isValid}`);
        console.log(`  Errors: ${validation.errors.join(', ')}`);
        failed++;
      }
    } catch (error) {
      console.log(`✗ Validation Test ${index + 1}: ${test.description} - ERROR`);
      console.log(`  Formula: ${test.formula}`);
      console.log(`  Error: ${error.message}`);
      failed++;
    }
  });
  
  console.log(`\nValidation Test Results: ${passed} passed, ${failed} failed`);
  return { passed, failed, total: tests.length };
}

// Run all tests
export function runAllTests() {
  console.log('='.repeat(50));
  console.log('FORMULA ENGINE TEST SUITE');
  console.log('='.repeat(50));
  
  const basicTests = runFormulaEngineTests();
  const contextTests = runContextTests();
  const validationTests = runValidationTests();
  
  const totalPassed = basicTests.passed + contextTests.passed + validationTests.passed;
  const totalFailed = basicTests.failed + contextTests.failed + validationTests.failed;
  const totalTests = basicTests.total + contextTests.total + validationTests.total;
  
  console.log('\n' + '='.repeat(50));
  console.log('OVERALL RESULTS');
  console.log('='.repeat(50));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${totalPassed}`);
  console.log(`Failed: ${totalFailed}`);
  console.log(`Success Rate: ${Math.round((totalPassed / totalTests) * 100)}%`);
  
  return {
    passed: totalPassed,
    failed: totalFailed,
    total: totalTests,
    successRate: Math.round((totalPassed / totalTests) * 100)
  };
}
