/**
 * Formula Templates API Route
 * 
 * Provides endpoints for managing formula templates and
 * getting pre-built formulas for common business calculations.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/lib/auth';
import { FormulaRepository } from '@/lib/repositories/formula.repository';

const formulaRepository = new FormulaRepository();

// Query parameters schema
const getTemplatesSchema = z.object({
  category: z.string().optional(),
});

/**
 * GET /api/reports/formulas/templates
 * Get formula templates
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const { category } = getTemplatesSchema.parse(Object.fromEntries(searchParams.entries()));

    const templates = await formulaRepository.getTemplateFormulas(category);

    // Group templates by category
    const groupedTemplates = templates.reduce((acc, template) => {
      const cat = template.category;
      if (!acc[cat]) {
        acc[cat] = [];
      }
      acc[cat].push(template);
      return acc;
    }, {} as Record<string, typeof templates>);

    return NextResponse.json({
      success: true,
      data: {
        templates: groupedTemplates,
        categories: Object.keys(groupedTemplates),
        totalTemplates: templates.length,
      },
    });

  } catch (error) {
    console.error('Error fetching formula templates:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch formula templates' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reports/formulas/templates/seed
 * Seed database with default formula templates
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !['ADMIN'].includes(session.user.role?.toUpperCase() || '')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Default formula templates
    const defaultTemplates = [
      // Mathematical Templates
      {
        name: 'Percentage Calculation',
        description: 'Calculate percentage of value relative to total',
        formula: 'PERCENTAGE(value, total)',
        category: 'MATHEMATICAL',
        variables: ['value', 'total'],
        returnType: 'NUMBER',
      },
      {
        name: 'Growth Rate',
        description: 'Calculate growth percentage between current and previous values',
        formula: 'GROWTH(current, previous)',
        category: 'MATHEMATICAL',
        variables: ['current', 'previous'],
        returnType: 'NUMBER',
      },
      {
        name: 'Average Calculation',
        description: 'Calculate average of multiple values',
        formula: 'AVERAGE(value1, value2, value3)',
        category: 'STATISTICAL',
        variables: ['value1', 'value2', 'value3'],
        returnType: 'NUMBER',
      },

      // Business Templates
      {
        name: 'Profit Margin',
        description: 'Calculate profit margin percentage',
        formula: 'MARGIN(revenue, cost)',
        category: 'BUSINESS',
        variables: ['revenue', 'cost'],
        returnType: 'NUMBER',
      },
      {
        name: 'Discount Amount',
        description: 'Calculate discounted amount',
        formula: 'DISCOUNT(amount, discountPercent)',
        category: 'BUSINESS',
        variables: ['amount', 'discountPercent'],
        returnType: 'NUMBER',
      },
      {
        name: 'Tax Calculation',
        description: 'Calculate tax amount',
        formula: 'TAX(amount, taxPercent)',
        category: 'BUSINESS',
        variables: ['amount', 'taxPercent'],
        returnType: 'NUMBER',
      },
      {
        name: 'Total with Tax',
        description: 'Calculate total amount including tax',
        formula: 'amount + TAX(amount, taxPercent)',
        category: 'BUSINESS',
        variables: ['amount', 'taxPercent'],
        returnType: 'NUMBER',
      },

      // Date Templates
      {
        name: 'Days Between Dates',
        description: 'Calculate days between two dates',
        formula: 'DAYS(endDate, startDate)',
        category: 'DATE',
        variables: ['endDate', 'startDate'],
        returnType: 'NUMBER',
      },
      {
        name: 'Contract Duration (Months)',
        description: 'Calculate contract duration in months',
        formula: 'MONTHS(endDate, startDate)',
        category: 'DATE',
        variables: ['endDate', 'startDate'],
        returnType: 'NUMBER',
      },

      // Conditional Templates
      {
        name: 'Status Based on Amount',
        description: 'Determine status based on amount threshold',
        formula: 'IF(amount > 10000, "High Value", "Standard")',
        category: 'CONDITIONAL',
        variables: ['amount'],
        returnType: 'STRING',
      },
      {
        name: 'Priority Based on Days',
        description: 'Determine priority based on days remaining',
        formula: 'IF(daysRemaining < 7, "Urgent", IF(daysRemaining < 30, "High", "Normal"))',
        category: 'CONDITIONAL',
        variables: ['daysRemaining'],
        returnType: 'STRING',
      },

      // Complex Business Templates
      {
        name: 'AMC Renewal Rate',
        description: 'Calculate AMC renewal rate percentage',
        formula: 'PERCENTAGE(renewedContracts, totalContracts)',
        category: 'BUSINESS',
        variables: ['renewedContracts', 'totalContracts'],
        returnType: 'NUMBER',
      },
      {
        name: 'Service Efficiency',
        description: 'Calculate service efficiency based on completion time',
        formula: 'IF(actualHours <= plannedHours, 100, ROUND(plannedHours / actualHours * 100, 2))',
        category: 'BUSINESS',
        variables: ['actualHours', 'plannedHours'],
        returnType: 'NUMBER',
      },
      {
        name: 'Customer Satisfaction Score',
        description: 'Calculate weighted customer satisfaction score',
        formula: 'ROUND((serviceRating * 0.4 + responseRating * 0.3 + qualityRating * 0.3), 2)',
        category: 'BUSINESS',
        variables: ['serviceRating', 'responseRating', 'qualityRating'],
        returnType: 'NUMBER',
      },
    ];

    const createdTemplates = [];
    
    for (const template of defaultTemplates) {
      try {
        const created = await formulaRepository.createFormula(
          {
            ...template,
            isTemplate: true,
          },
          session.user.id
        );
        createdTemplates.push(created);
      } catch (error) {
        // Skip if template already exists
        if (error.code !== 'P2002') {
          console.error('Error creating template:', template.name, error);
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        created: createdTemplates.length,
        total: defaultTemplates.length,
        templates: createdTemplates,
      },
      message: `Successfully created ${createdTemplates.length} formula templates`,
    });

  } catch (error) {
    console.error('Error seeding formula templates:', error);
    return NextResponse.json(
      { error: 'Failed to seed formula templates' },
      { status: 500 }
    );
  }
}
